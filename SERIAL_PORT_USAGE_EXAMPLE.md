# Serial Port Refactored - Usage Examples

## Overview

The serial port code has been completely refactored to handle all possible errors and edge cases. Here's how to use the new API:

## Basic Usage

```typescript
import { 
  connectToSerialPort, 
  writeToSerialPort, 
  disconnectSerialPort,
  serialPortData, 
  serialPortError,
  portState,
  isPortReady,
  getPortState,
  forceReconnect
} from '$lib/serial-port';

// Connect to serial port
const connected = await connectToSerialPort();
if (connected) {
  console.log('Successfully connected!');
} else {
  console.log('Failed to connect');
}

// Write data
const success = await writeToSerialPort('Hello Arduino!');
if (success) {
  console.log('Data sent successfully');
}

// Listen to data (automatic)
serialPortData.subscribe(data => {
  console.log('Received:', data);
});

// Listen to errors
serialPortError.subscribe(error => {
  if (error) {
    console.error('Serial port error:', error.message);
  }
});

// Monitor connection state
portState.subscribe(state => {
  console.log('Port state:', state);
});
```

## Advanced Configuration

```typescript
// Connect with custom configuration
const connected = await connectToSerialPort({
  baudRate: 115200,
  dataBits: 8,
  stopBits: 1,
  parity: 'none'
});
```

## Error Handling

```typescript
// Check if port is ready before operations
if (isPortReady()) {
  await writeToSerialPort('data');
}

// Handle connection failures
const connected = await connectToSerialPort();
if (!connected) {
  const state = getPortState();
  console.log('Connection failed:', state.error);
  
  // Try to force reconnect
  const reconnected = await forceReconnect();
  if (reconnected) {
    console.log('Reconnection successful');
  }
}

// Proper cleanup
await disconnectSerialPort();
```

## Key Improvements

1. **Comprehensive Error Handling**: All operations now have proper try-catch blocks
2. **Timeout Protection**: Operations won't hang indefinitely
3. **Automatic Reconnection**: Handles device disconnections gracefully
4. **State Management**: Track connection, reading, and writing states
5. **Data Validation**: Validates input data before sending
6. **Resource Cleanup**: Proper cleanup of readers/writers and port connections
7. **Browser Compatibility**: Checks for Web Serial API support
8. **Concurrent Operation Prevention**: Prevents multiple simultaneous operations
9. **Permission Handling**: Proper handling of user permission scenarios
10. **Detailed Error Types**: Categorized error types for better debugging

## Breaking Changes

- `connectToSerialPort()` now returns `Promise<boolean>` instead of `Promise<void>`
- No more automatic page reload when no ports are found
- Functions now return boolean success indicators
