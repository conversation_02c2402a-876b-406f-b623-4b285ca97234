{"name": "project-2024-nkf-kidney-trivia-gameshow", "version": "0.0.1", "type": "module", "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "format": "prettier --write .", "lint": "prettier --check . && eslint ."}, "devDependencies": {"@eslint/compat": "^1.2.9", "@sveltejs/adapter-node": "^5.2.12", "@sveltejs/enhanced-img": "^0.5.0", "@sveltejs/kit": "^2.20.8", "@sveltejs/vite-plugin-svelte": "^5.0.3", "@tailwindcss/vite": "^4.1.5", "@types/dom-serial": "^1.0.6", "@types/howler": "^2.2.12", "@types/node": "^22.15.14", "clsx": "^2.1.1", "eslint": "^9.26.0", "eslint-config-prettier": "^10.1.2", "eslint-plugin-svelte": "^3.5.1", "globals": "^16.0.0", "howler": "^2.2.4", "prettier": "^3.5.3", "prettier-plugin-svelte": "^3.3.3", "prettier-plugin-tailwindcss": "^0.6.11", "svelte": "^5.28.2", "svelte-check": "^4.1.7", "tailwind-merge": "^3.2.0", "tailwindcss": "^4.0.17", "typescript": "^5.8.3", "typescript-eslint": "^8.32.0", "vite": "^6.3.5"}}