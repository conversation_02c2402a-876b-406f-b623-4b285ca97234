<script lang="ts">
	import { appStore, QUESTIONS } from '$lib/app.state';
	import SoloQuestionContainer from '$lib/components/solo-question-container.svelte';
	import ScoreLabel from '$lib/components/score-label.svelte';
	import { player1, player2 } from '$lib/player.state';
	import SimplePngSequencePlayer from '$lib/components/SimplePngSequencePlayer.svelte';
	import { onMount } from 'svelte';
	import { playAudio, stopAudio } from '$lib/audio';
	import MultiplayerQuestionContainer from '$lib/components/multiplayer-question-container.svelte';

	let currentQuestionIndex = $state(0);
	let currentQuestion = $derived(
		QUESTIONS[$appStore.language][$appStore.questionIndexs[currentQuestionIndex]]
	);

	function nextQuestion() {
		if (currentQuestionIndex === $appStore.questionIndexs.length - 1) {
			return false;
		}
		currentQuestionIndex++;

		return true;
	}

	onMount(() => {
		stopAudio('bgm');
		playAudio('bgm2');
	});
</script>

<section class="bg-gameplay flex size-full flex-col">
	<SimplePngSequencePlayer
		class="absolute top-0 left-0"
		imageUrls={Array.from(
			{ length: 900 },
			(_, i) => `/assets/pngSequences/gameplay/${String(i).padStart(5, '0')}.webp`
		)}
		fps={60}
		loop={true}
	/>

	<MultiplayerQuestionContainer
		questionIndex={currentQuestionIndex}
		{currentQuestion}
		{nextQuestion}
	/>
</section>
