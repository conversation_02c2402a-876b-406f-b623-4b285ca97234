<script lang="ts">
	import '../app.css';

	import { handleKeydown, handleKeyup } from '$lib/button.state';
	import { onMount } from 'svelte';
	import { connectToSerialPort, serialPortData } from '$lib/serial-port';

	let { children } = $props();

	onMount(() => {
		// Listen for keydown events globally
		window.addEventListener('keydown', handleKeydown);
		window.addEventListener('keyup', handleKeyup);

		connectToSerialPort();

		serialPortData.subscribe((data) => {
			console.log('Received serial data:', data);
		});

		// Clean up the event listener on component unmount
		return () => {
			window.removeEventListener('keydown', handleKeydown);
			window.removeEventListener('keyup', handleKeyup);
		};
	});
</script>

<section class="relative h-[1080px] w-[1920px] overflow-hidden">
	<button class="absolute top-0 left-0 size-10 opacity-0 z-50" onclick={() => connectToSerialPort()}>
		a
	</button>

	<video
		class="absolute top-0 left-0 z-10 size-full"
		src="/assets/bg/primary.mp4"
		autoplay
		loop
		muted
	></video>
	<div class="absolute top-0 left-0 z-20 size-full">
		{@render children()}
	</div>
</section>
