<script lang="ts">
	import '../app.css';

	import { handleKeydown, handleKeyup } from '$lib/button.state';
	import { onMount } from 'svelte';

	let { children } = $props();

	onMount(() => {
		// Listen for keydown events globally
		window.addEventListener('keydown', handleKeydown);
		window.addEventListener('keyup', handleKeyup);

		// Clean up the event listener on component unmount
		return () => {
			window.removeEventListener('keydown', handleKeydown);
			window.removeEventListener('keyup', handleKeyup);
		};
	});
</script>

<section class="relative h-[1080px] w-[1920px] overflow-hidden">
	<video
		class="absolute top-0 left-0 z-10 size-full"
		src="/assets/bg/primary.mp4"
		autoplay
		loop
		muted
	></video>
	<div class="absolute top-0 left-0 z-20 size-full">
		{@render children()}
	</div>
</section>
