<script lang="ts">
	import { goto } from '$app/navigation';
	import { appStore } from '$lib/app.state';
	import { player1, player2 } from '$lib/player.state';
	import { getRfidValue } from '$lib/services/rfid';
	import { onMount } from 'svelte';

	let listenRfidInterval = $state<NodeJS.Timeout>();
	let rfidScannedSuccessfully = $state(false);

	function checkScannedPlayers(player1Scanned: boolean, player2Scanned: boolean) {
		// check if the game should stop
		// if the game is solo and the player scanned is the same as the player who started the game
		// if the game is multiplayer and both players scanned
		const shouldStop =
			($appStore.gameMode === 'solo' &&
				(($appStore.gameStartBy === 'player1' && player1Scanned) ||
					($appStore.gameStartBy === 'player2' && player2Scanned))) ||
			($appStore.gameMode === 'multiplayer' && player1Scanned && player2Scanned);

		if (shouldStop) {
			rfidScannedSuccessfully = true;
			clearInterval(listenRfidInterval);

			setTimeout(() => {
				goto('/how-to-play');
			}, 3000);
		}
	}

	$effect(() => checkScannedPlayers(!!$player1.rfid, !!$player2.rfid));

	onMount(() => {
		if ($appStore.gameMode === 'multiplayer') {
			$player1.locked = false;
			$player2.locked = false;
		}

		listenRfidInterval = setInterval(async () => {
			const rfid = await getRfidValue();
			if (rfid[0] && rfid[0] !== $player1.rfid) $player1.rfid = rfid[0];
			if (rfid[1] && rfid[1] !== $player2.rfid) $player2.rfid = rfid[1];
		}, 1000);

		return () => {
			clearInterval(listenRfidInterval);
		};
	});
</script>

<section class="flex size-full flex-col items-center justify-center gap-y-16 text-white">
	<h1 class="font-parkin text-[70px] font-extrabold uppercase">TAP YOUR RFID to start</h1>

	<div class="flex flex-col items-center justify-between">
		<div class="font-parkin relative text-white">
			<span class="absolute top-4 left-22 text-[83.16px] font-bold">S</span>
			<div class="absolute top-10 left-46 w-68 truncate text-center">
				<span class="text-[42px] font-bold">Player 1</span>
			</div>
			<img src="/assets/bg/rfid-scanning-frame.png" alt="frame" />
		</div>

		<div class="flex items-center space-x-6">
			<div class="flex items-center gap-x-5">
				<span class="scale-up size-7 rounded-full bg-white"></span>
				<span class="scale-up ani-delay-1 size-7 rounded-full bg-white"></span>
				<span class="scale-up ani-delay-2 size-7 rounded-full bg-white"></span>
			</div>
			<p class="font-marqona text-[40px] leading-none tracking-widest">
				{rfidScannedSuccessfully ? 'SUCCESS!' : 'SCANNING'}
			</p>
			<div class="flex items-center gap-x-5">
				<span class="ani-delay-2 scale-up size-7 rounded-full bg-white"></span>
				<span class="ani-delay-1 scale-up size-7 rounded-full bg-white"></span>
				<span class="scale-up size-7 rounded-full bg-white"></span>
			</div>
		</div>
	</div>
</section>

<style>
	.scale-up {
		animation: scale-up 0.6s ease-in-out infinite forwards;
	}

	.ani-delay-1 {
		animation-delay: 0.1s;
	}

	.ani-delay-2 {
		animation-delay: 0.15s;
	}

	@keyframes scale-up {
		0%,
		100% {
			transform: scale(0.2);
		}
		50% {
			transform: scale(1);
		}
	}
</style>
