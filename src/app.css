@import "tailwindcss";

@theme {
	--font-marqona:
		'MAR<PERSON><PERSON><PERSON>', ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji',
		'Segoe UI Symbol', 'Noto Color Emoji';
	--font-parkin:
		'Parkin', ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji',
		'Segoe UI Symbol', 'Noto Color Emoji';
	--font-phluff:
		'Phluff', ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji',
		'Segoe UI Symbol', 'Noto Color Emoji';

	--background-image-landing: url('/assets/bg/landing.png');
	--background-image-primary: url('/assets/bg/primary.png');
	--background-image-score: url('/assets/bg/score.png');
	--background-image-gameplay: url('/assets/bg/gameplay.png');
	--background-image-timesup: url('/assets/bg/timesup.png');

	--drop-shadow-button: -3.2px 7.2px 14px #28507299;
	--drop-shadow-button-focus: 0px 4px 10px #ffb2b2;
}

/*
  The default border color has changed to `currentColor` in Tailwind CSS v4,
  so we've added these compatibility styles to make sure everything still
  looks the same as it did with Tailwind CSS v3.

  If we ever want to remove these styles, we need to add an explicit border
  color utility to any element that depends on these defaults.
*/
@layer base {
	*,
	::after,
	::before,
	::backdrop,
	::file-selector-button {
		border-color: var(--color-gray-200, currentColor);
	}
}

@layer base {
	@font-face {
		font-family: 'MARQONA';
		font-style: normal;
		font-weight: 400;
		font-display: swap;
		src: url(/assets/fonts/marqona/MARQONA.ttf) format('truetype');
	}

	@font-face {
		font-family: 'Parkin';
		font-style: normal;
		font-weight: 400;
		font-display: swap;
		src: url(/assets/fonts/parkin/Parkin-Regular.ttf) format('truetype');
	}

	@font-face {
		font-family: 'Parkin';
		font-style: normal;
		font-weight: 700;
		font-display: swap;
		src: url(/assets/fonts/parkin/Parkin-Bold.ttf) format('truetype');
	}

	@font-face {
		font-family: 'Parkin';
		font-style: normal;
		font-weight: 800;
		font-display: swap;
		src: url(/assets/fonts/parkin/Parkin-ExtraBold.ttf) format('truetype');
	}

	@font-face {
		font-family: 'Parkin';
		font-style: normal;
		font-weight: 900;
		font-display: swap;
		src: url(/assets/fonts/parkin/Parkin-Black.ttf) format('truetype');
	}

	@font-face {
		font-family: 'Phluff';
		font-style: normal;
		font-weight: 600;
		font-display: swap;
		src: url(/assets/fonts/phluff/Phluff-SemiBold.ttf) format('truetype');
	}
}

@layer utilities {
	.breathing {
		animation: breathing 1.25s ease-in-out infinite;
	}

	@keyframes breathing {
		0% {
			transform: scale(1.1);
		}

		50% {
			transform: scale(1);
		}

		100% {
			transform: scale(1.1);
		}
	}
}
