import { Howl } from 'howler';

type AudioKey = 'bgm' | 'bgm2' | 'scan' | 'tickTock' | 'gameOver' | 'correct';

const audioStore = {
	bgm: new Howl({ src: ['/assets/audio/bgm.wav'], loop: true }),
	bgm2: new Howl({ src: ['/assets/audio/bgm2.mp3'], loop: true }),
	scan: new Howl({ src: ['/assets/audio/scan.wav'] }),
	tickTock: new Howl({ src: ['/assets/audio/tick-tock.wav'], loop: true }),
	gameOver: new Howl({ src: ['/assets/audio/game-over.mp3'] }),
	correct: new Howl({ src: ['/assets/audio/correct.wav'] }),
};

export const playAudio = (key: AudioKey) => {
	audioStore[key].play();
};

export const stopAudio = (key: AudioKey) => {
	audioStore[key].stop();
};
