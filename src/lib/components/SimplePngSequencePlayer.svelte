<script lang="ts">
  import { onDestroy } from 'svelte'; // Auto-imported in Svelte 5, but explicit is fine.

  type Props = {
    imageUrls: string[]; // Making this required by not providing a default
    fps?: number;
    loop?: boolean;
    class?: string; 
  };

  let {
    imageUrls,
    fps = 24,
    loop = true,
    class: className,
  }: Props = $props();

  let currentFrameIndex = $state(0);
  let animationFrameId: number | null = null;
  let lastFrameTime = 0;

  // Internal reactive FPS to allow $derived to react if fps prop changes
  let reactiveInternalFps = $state(fps); 
  const frameInterval = $derived(1000 / reactiveInternalFps);
  
  // Directly use imageUrls; browser handles loading/caching.
  const currentImageSrc = $derived(imageUrls?.[currentFrameIndex] ?? ''); 

  function animate(timestamp: number) {
    // If no images, or if animation was stopped externally, clear frame and exit.
    if (!imageUrls || imageUrls.length === 0 || animationFrameId === null) {
      if (animationFrameId) cancelAnimationFrame(animationFrameId);
      animationFrameId = null;
      return;
    }
    
    animationFrameId = requestAnimationFrame(animate);
    const elapsed = timestamp - lastFrameTime;

    if (elapsed >= frameInterval) {
      lastFrameTime = timestamp - (elapsed % frameInterval);
      let nextFrame = currentFrameIndex + 1;

      if (nextFrame >= imageUrls.length) {
        if (loop) {
          nextFrame = 0;
        } else {
          // Stop animation if not looping and end is reached
          if (animationFrameId) cancelAnimationFrame(animationFrameId);
          animationFrameId = null;
          return; 
        }
      }
      currentFrameIndex = nextFrame;
    }
  }

  function startOrRestartAnimation() {
    if (animationFrameId) {
      cancelAnimationFrame(animationFrameId);
      animationFrameId = null;
    }
    // Only start if there are images to show
    if (imageUrls && imageUrls.length > 0) {
        currentFrameIndex = 0; // Reset to first frame
        lastFrameTime = performance.now();
        animationFrameId = requestAnimationFrame(animate);
    } else {
        currentFrameIndex = 0; // Ensure it's reset if urls become empty
    }
  }

  // Effect to handle changes to imageUrls or fps prop
  $effect(() => {
    if (fps !== reactiveInternalFps) {
        reactiveInternalFps = fps;
    }
    // This effect will re-run if imageUrls or reactiveInternalFps changes,
    // effectively restarting the animation with new settings/sources.
    startOrRestartAnimation();
  });

  onDestroy(() => {
    if (animationFrameId) {
      cancelAnimationFrame(animationFrameId);
      animationFrameId = null;
    }
  });

</script>

{#if imageUrls && imageUrls.length > 0 && currentImageSrc}
  <img src={currentImageSrc} alt="PNG Sequence" class={className} />
{/if}

<style>
  img {
    display: block; /* Default styling */
    /* Optional: for pixel art if images are scaled up */
    /* image-rendering: pixelated; */
    /* image-rendering: crisp-edges; */
  }
</style> 