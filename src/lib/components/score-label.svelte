<script lang="ts">
	import { cn } from '$lib';

	type Props = {
		playerName: string;
		score: number;
		side?: 'left' | 'right';
	};

	const { playerName, score, side = 'left' }: Props = $props();
</script>

<div class="font-parkin relative w-fit leading-none font-bold text-white uppercase">
	<img src={`/assets/el/${side}-score.png`} alt="score" />

	<span
		class={cn('absolute top-7 text-[49.67px] tracking-[0.062em]', {
			'left-32.5': side === 'left',
			'right-32.5': side === 'right'
		})}
	>
		{playerName}
	</span>

	<span
		class={cn('absolute top-23.5 text-[79.48px] tracking-[0.062em]', {
			'left-13': side === 'left',
			'right-11': side === 'right'
		})}
	>
		{playerName.charAt(0)}
	</span>

	<span
		class={cn('absolute top-23.5 w-31.5 text-center text-[77.9px]', {
			'right-33': side === 'left',
			'left-33': side === 'right'
		})}
	>
		{score}
	</span>
</div>
