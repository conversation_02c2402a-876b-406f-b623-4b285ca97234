import { persisted } from '$lib/persistedStore';
import { get, writable } from 'svelte/store';

const connectedPort = writable<SerialPort | null>(null);
export const serialPortData = persisted<string>("knob",'5');

export const connectToSerialPort = async () => {
	const ports = await navigator.serial.getPorts();

	if (!ports.length) {
		await navigator.serial.requestPort();
		window.location.reload();
	} else {
		console.log(`Connect to ${JSON.stringify(ports[0].getInfo())}`);
		connectedPort.set(ports[0]);
		listenForData(ports[0]);
		return;
	}
};

export const writeToSerialPort = async (data: string) => {
	const _connectedPort = get(connectedPort);

	if (!_connectedPort) {
		await connectToSerialPort();
	} else {
		const writer = _connectedPort.writable.getWriter();
		console.log('Writing data to serial port:', data);
		
		await writer.write(new TextEncoder().encode(data));
		writer.releaseLock();
	}
};

const listenForData = async (port: SerialPort) => {
	if (!port.readable) {
		await port.open({ baudRate: 9600 });
	}

	let receivedData = '';

	const reader = port.readable.getReader();

	try {
		while (true) {
			const { value, done } = await reader.read();
			if (done) {
				break;
			}

			const decoder = new TextDecoder();

			receivedData += decoder.decode(value, { stream: true });

			// Split received data by newline characters
			const lines = receivedData.split('\n');
			// Keep the last line (could be incomplete)
			receivedData = lines.pop() || '';

			// Process complete lines
			for (const line of lines) {
				serialPortData.set(Number.isInteger(parseInt(line.trim())) ? line.trim() : "0");
			}
		}
	} catch (error) {
		console.log(`Have error: ${JSON.stringify(error)}`, error);
		// Handle |error|…
		alert('Serial port error');
	} finally {
		reader.releaseLock();
	}
};
