import { get, writable } from 'svelte/store';

// Enhanced types for better error handling
export interface SerialPortConfig {
	baudRate: number;
	dataBits?: 7 | 8;
	stopBits?: 1 | 2;
	parity?: 'none' | 'even' | 'odd';
	bufferSize?: number;
	flowControl?: 'none' | 'hardware';
}

export interface SerialPortState {
	isConnected: boolean;
	isConnecting: boolean;
	isReading: boolean;
	isWriting: boolean;
	error: string | null;
	lastActivity: Date | null;
}

export interface SerialPortError {
	type:
		| 'connection'
		| 'permission'
		| 'timeout'
		| 'data'
		| 'browser_support'
		| 'port_busy'
		| 'unknown';
	message: string;
	originalError?: Error;
	timestamp: Date;
}

// Default configuration
const DEFAULT_CONFIG: SerialPortConfig = {
	baudRate: 9600,
	dataBits: 8,
	stopBits: 1,
	parity: 'none',
	bufferSize: 255,
	flowControl: 'none'
};

// Timeout constants
const TIMEOUTS = {
	CONNECTION: 10000, // 10 seconds
	READ: 5000, // 5 seconds
	WRITE: 3000, // 3 seconds
	RECONNECT_DELAY: 2000 // 2 seconds
} as const;

// Store definitions
const connectedPort = writable<SerialPort | null>(null);
const portState = writable<SerialPortState>({
	isConnected: false,
	isConnecting: false,
	isReading: false,
	isWriting: false,
	error: null,
	lastActivity: null
});

export const serialPortData = writable<string>('5');
export const serialPortError = writable<SerialPortError | null>(null);

// Internal state tracking
let currentReader: ReadableStreamDefaultReader<Uint8Array> | null = null;
let currentWriter: WritableStreamDefaultWriter<Uint8Array> | null = null;
let reconnectAttempts = 0;
const maxReconnectAttempts = 3;
let isCleaningUp = false;

// Utility functions
const isWebSerialSupported = (): boolean => {
	return typeof navigator !== 'undefined' && 'serial' in navigator;
};

const createError = (
	type: SerialPortError['type'],
	message: string,
	originalError?: Error
): SerialPortError => {
	return {
		type,
		message,
		originalError,
		timestamp: new Date()
	};
};

const updatePortState = (updates: Partial<SerialPortState>) => {
	portState.update((state) => ({ ...state, ...updates }));
};

const setError = (error: SerialPortError) => {
	serialPortError.set(error);
	updatePortState({ error: error.message });
	console.error(`Serial Port Error [${error.type}]:`, error.message, error.originalError);
};

const clearError = () => {
	serialPortError.set(null);
	updatePortState({ error: null });
};

// Cleanup function
const cleanup = async () => {
	if (isCleaningUp) return;
	isCleaningUp = true;

	try {
		// Release reader
		if (currentReader) {
			try {
				await currentReader.cancel();
				currentReader.releaseLock();
			} catch (e) {
				console.warn('Error releasing reader:', e);
			}
			currentReader = null;
		}

		// Release writer
		if (currentWriter) {
			try {
				await currentWriter.close();
				currentWriter.releaseLock();
			} catch (e) {
				console.warn('Error releasing writer:', e);
			}
			currentWriter = null;
		}

		// Close port
		const port = get(connectedPort);
		if (port && port.readable && port.writable) {
			try {
				await port.close();
			} catch (e) {
				console.warn('Error closing port:', e);
			}
		}

		connectedPort.set(null);
		updatePortState({
			isConnected: false,
			isConnecting: false,
			isReading: false,
			isWriting: false
		});
	} finally {
		isCleaningUp = false;
	}
};

// Timeout wrapper for promises
const withTimeout = <T>(
	promise: Promise<T>,
	timeoutMs: number,
	errorMessage: string
): Promise<T> => {
	return Promise.race([
		promise,
		new Promise<never>((_, reject) => {
			setTimeout(() => reject(new Error(errorMessage)), timeoutMs);
		})
	]);
};

// Validation functions
const validateData = (data: string): boolean => {
	if (typeof data !== 'string') return false;
	if (data.length === 0) return false;
	if (data.length > 1024) return false; // Reasonable size limit
	return true;
};

const isPortDisconnected = (error: Error): boolean => {
	const message = error.message.toLowerCase();
	return (
		message.includes('disconnected') ||
		message.includes('device not found') ||
		message.includes('port not open') ||
		message.includes('network error')
	);
};

// Main connection function with comprehensive error handling
export const connectToSerialPort = async (
	config: Partial<SerialPortConfig> = {}
): Promise<boolean> => {
	// Check browser support
	if (!isWebSerialSupported()) {
		setError(createError('browser_support', 'Web Serial API is not supported in this browser'));
		return false;
	}

	// Prevent multiple simultaneous connection attempts
	const currentState = get(portState);
	if (currentState.isConnecting) {
		console.warn('Connection attempt already in progress');
		return false;
	}

	if (currentState.isConnected) {
		console.log('Already connected to serial port');
		return true;
	}

	updatePortState({ isConnecting: true });
	clearError();

	try {
		const finalConfig = { ...DEFAULT_CONFIG, ...config };

		// Get available ports with timeout
		const ports = await withTimeout(
			navigator.serial.getPorts(),
			TIMEOUTS.CONNECTION,
			'Timeout while getting serial ports'
		);

		let selectedPort: SerialPort;

		if (ports.length === 0) {
			// Request port from user with timeout
			try {
				selectedPort = await withTimeout(
					navigator.serial.requestPort(),
					TIMEOUTS.CONNECTION,
					'Timeout while requesting serial port'
				);
			} catch (error) {
				if (error instanceof Error) {
					if (error.name === 'NotAllowedError') {
						setError(createError('permission', 'User denied access to serial port'));
					} else if (error.name === 'AbortError') {
						setError(createError('permission', 'User cancelled port selection'));
					} else {
						setError(createError('connection', 'Failed to request serial port', error));
					}
				}
				return false;
			}
		} else {
			selectedPort = ports[0];
		}

		// Check if port is already open
		if (selectedPort.readable && selectedPort.writable) {
			console.log('Port is already open, using existing connection');
		} else {
			// Open port with timeout and error handling
			try {
				await withTimeout(
					selectedPort.open(finalConfig),
					TIMEOUTS.CONNECTION,
					'Timeout while opening serial port'
				);
			} catch (error) {
				if (error instanceof Error) {
					if (error.name === 'InvalidStateError') {
						setError(
							createError('port_busy', 'Serial port is already in use by another application')
						);
					} else if (error.name === 'NetworkError') {
						setError(
							createError(
								'connection',
								'Failed to open serial port - device may be disconnected',
								error
							)
						);
					} else {
						setError(createError('connection', 'Failed to open serial port', error));
					}
				}
				return false;
			}
		}

		// Verify port is properly opened
		if (!selectedPort.readable || !selectedPort.writable) {
			setError(createError('connection', 'Port opened but streams are not available'));
			return false;
		}

		console.log(`Successfully connected to serial port: ${JSON.stringify(selectedPort.getInfo())}`);

		connectedPort.set(selectedPort);
		updatePortState({
			isConnected: true,
			isConnecting: false,
			lastActivity: new Date()
		});

		// Start listening for data
		listenForData(selectedPort);

		reconnectAttempts = 0; // Reset reconnect attempts on successful connection
		return true;
	} catch (error) {
		const serialError = error instanceof Error ? error : new Error('Unknown connection error');
		setError(createError('connection', 'Failed to connect to serial port', serialError));
		return false;
	} finally {
		updatePortState({ isConnecting: false });
	}
};

// Enhanced write function with comprehensive error handling
export const writeToSerialPort = async (data: string): Promise<boolean> => {
	// Validate input data
	if (!validateData(data)) {
		setError(createError('data', 'Invalid data: must be a non-empty string under 1024 characters'));
		return false;
	}

	const currentState = get(portState);

	// Check if already writing
	if (currentState.isWriting) {
		console.warn('Write operation already in progress');
		return false;
	}

	// Check connection status
	if (!currentState.isConnected) {
		console.log('Not connected, attempting to connect...');
		const connected = await connectToSerialPort();
		if (!connected) {
			return false;
		}
	}

	const port = get(connectedPort);
	if (!port || !port.writable) {
		setError(createError('connection', 'Serial port is not writable'));
		return false;
	}

	updatePortState({ isWriting: true });
	clearError();

	try {
		// Get writer with timeout
		let writer: WritableStreamDefaultWriter<Uint8Array>;
		try {
			writer = port.writable.getWriter();
			currentWriter = writer;
		} catch (error) {
			console.warn('Failed to get writer:', error);
			setError(createError('port_busy', 'Failed to get writer - port may be busy'));
			return false;
		}

		console.log('Writing data to serial port:', data);

		// Encode and write data with timeout
		const encodedData = new TextEncoder().encode(data);

		try {
			await withTimeout(
				writer.write(encodedData),
				TIMEOUTS.WRITE,
				'Timeout while writing to serial port'
			);
		} catch (error) {
			if (error instanceof Error && isPortDisconnected(error)) {
				setError(createError('connection', 'Device disconnected during write operation', error));
				await cleanup();
			} else {
				setError(
					createError(
						'data',
						'Failed to write data to serial port',
						error instanceof Error ? error : new Error('Unknown write error')
					)
				);
			}
			return false;
		} finally {
			// Always release the writer
			try {
				writer.releaseLock();
				currentWriter = null;
			} catch (e) {
				console.warn('Error releasing writer lock:', e);
			}
		}

		updatePortState({ lastActivity: new Date() });
		console.log('Successfully wrote data to serial port');
		return true;
	} catch (error) {
		const serialError = error instanceof Error ? error : new Error('Unknown write error');
		setError(createError('data', 'Failed to write to serial port', serialError));
		return false;
	} finally {
		updatePortState({ isWriting: false });
	}
};

// Enhanced listen function with comprehensive error handling and reconnection
const listenForData = async (port: SerialPort): Promise<void> => {
	const currentState = get(portState);

	// Prevent multiple simultaneous read operations
	if (currentState.isReading) {
		console.warn('Already listening for data');
		return;
	}

	updatePortState({ isReading: true });

	let receivedData = '';
	let reader: ReadableStreamDefaultReader<Uint8Array> | null = null;

	try {
		// Ensure port is open and readable
		if (!port.readable) {
			console.log('Port not readable, attempting to open...');
			try {
				await withTimeout(
					port.open(DEFAULT_CONFIG),
					TIMEOUTS.CONNECTION,
					'Timeout while opening port for reading'
				);
			} catch (error) {
				setError(
					createError(
						'connection',
						'Failed to open port for reading',
						error instanceof Error ? error : new Error('Unknown open error')
					)
				);
				return;
			}
		}

		// Verify port is still readable after opening
		if (!port.readable) {
			setError(createError('connection', 'Port opened but readable stream is not available'));
			return;
		}

		// Get reader with error handling
		try {
			reader = port.readable.getReader();
			currentReader = reader;
		} catch (error) {
			console.warn('Failed to get reader:', error);
			setError(createError('port_busy', 'Failed to get reader - port may be busy'));
			return;
		}

		console.log('Started listening for serial port data');

		// Main reading loop with comprehensive error handling
		while (true) {
			try {
				// Read with timeout to prevent hanging
				const result = await withTimeout(
					reader.read(),
					TIMEOUTS.READ,
					'Timeout while reading from serial port'
				);

				const { value, done } = result;

				if (done) {
					console.log('Serial port reading completed (stream closed)');
					break;
				}

				if (!value || value.length === 0) {
					continue; // Skip empty reads
				}

				// Decode received data
				const decoder = new TextDecoder('utf-8', { fatal: false });
				const chunk = decoder.decode(value, { stream: true });

				receivedData += chunk;
				updatePortState({ lastActivity: new Date() });

				// Process complete lines
				const lines = receivedData.split('\n');
				receivedData = lines.pop() || ''; // Keep incomplete line

				// Process each complete line
				for (const line of lines) {
					const trimmedLine = line.trim();
					if (trimmedLine.length === 0) continue;

					// Validate and process the data
					try {
						const parsedValue = parseInt(trimmedLine);
						const finalValue = Number.isInteger(parsedValue) ? trimmedLine : '0';
						serialPortData.set(finalValue);
						console.log('Received serial data:', finalValue);
					} catch (parseError) {
						console.warn('Failed to parse serial data:', trimmedLine, parseError);
						serialPortData.set('0'); // Default fallback
					}
				}
			} catch (readError) {
				if (readError instanceof Error) {
					// Handle different types of read errors
					if (isPortDisconnected(readError)) {
						console.log('Device disconnected, attempting reconnection...');
						setError(
							createError('connection', 'Device disconnected during read operation', readError)
						);

						// Attempt reconnection if within limits
						if (reconnectAttempts < maxReconnectAttempts) {
							reconnectAttempts++;
							console.log(`Reconnection attempt ${reconnectAttempts}/${maxReconnectAttempts}`);

							await cleanup();
							await new Promise((resolve) => setTimeout(resolve, TIMEOUTS.RECONNECT_DELAY));

							// Try to reconnect
							const reconnected = await connectToSerialPort();
							if (reconnected) {
								console.log('Reconnection successful');
								return; // New listenForData will be started by connectToSerialPort
							}
						} else {
							setError(
								createError(
									'connection',
									`Failed to reconnect after ${maxReconnectAttempts} attempts`
								)
							);
						}
						break;
					} else if (readError.message.includes('timeout')) {
						// Timeout is not necessarily an error, just continue
						console.warn('Read timeout, continuing...');
						continue;
					} else {
						setError(createError('data', 'Error reading from serial port', readError));
						break;
					}
				} else {
					setError(createError('unknown', 'Unknown error during read operation'));
					break;
				}
			}
		}
	} catch (error) {
		const serialError = error instanceof Error ? error : new Error('Unknown listen error');
		setError(createError('data', 'Failed to listen for serial port data', serialError));
	} finally {
		// Cleanup reader
		if (reader) {
			try {
				await reader.cancel();
				reader.releaseLock();
				currentReader = null;
			} catch (e) {
				console.warn('Error cleaning up reader:', e);
			}
		}

		updatePortState({ isReading: false });
		console.log('Stopped listening for serial port data');
	}
};

// Disconnect function with proper cleanup
export const disconnectSerialPort = async (): Promise<boolean> => {
	try {
		await cleanup();
		console.log('Successfully disconnected from serial port');
		return true;
	} catch (error) {
		const serialError = error instanceof Error ? error : new Error('Unknown disconnect error');
		setError(createError('connection', 'Error during disconnect', serialError));
		return false;
	}
};

// Get current port state
export const getPortState = () => {
	return get(portState);
};

// Get current port info
export const getPortInfo = () => {
	const port = get(connectedPort);
	if (!port) return null;

	try {
		return port.getInfo();
	} catch (error) {
		console.warn('Failed to get port info:', error);
		return null;
	}
};

// Check if port is connected and ready
export const isPortReady = (): boolean => {
	const state = get(portState);
	const port = get(connectedPort);

	return (
		state.isConnected &&
		!state.isConnecting &&
		port !== null &&
		port.readable !== null &&
		port.writable !== null
	);
};

// Force reconnection (useful for recovery)
export const forceReconnect = async (): Promise<boolean> => {
	console.log('Forcing reconnection...');
	await cleanup();
	reconnectAttempts = 0; // Reset reconnect attempts
	return await connectToSerialPort();
};

// Export stores for external use
export { portState };

// Cleanup on page unload
if (typeof window !== 'undefined') {
	window.addEventListener('beforeunload', () => {
		cleanup().catch(console.error);
	});

	// Handle visibility change (tab switching)
	document.addEventListener('visibilitychange', () => {
		if (document.visibilityState === 'hidden') {
			// Optionally pause operations when tab is hidden
			console.log('Tab hidden, serial port operations continue...');
		} else {
			// Optionally resume or check connection when tab becomes visible
			console.log('Tab visible, checking serial port connection...');
			const state = get(portState);
			if (state.isConnected) {
				// Optionally verify connection is still active
			}
		}
	});
}
