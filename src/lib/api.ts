import { PUBLIC_API_KEY, PUBLIC_API_URL } from '$env/static/public';

export type Interaction = {
	id: string;
	appId: string;
	userId: string | null;
	endedAt: string | null;
	createdAt: string;
	updatedAt: string;
};

export type Event = {
	id: string;
	detail: string;
	interactionId: string;
	createdAt: string;
	updatedAt: string;
};

export async function startInteraction() {
	const response = await fetch(`${PUBLIC_API_URL}/interaction/start`, {
		method: 'POST',
		headers: {
			'Content-Type': 'application/json',
			'x-api-key': PUBLIC_API_KEY
		}
	});

	if (!response.ok) {
		throw new Error('Failed to start interaction');
	}

	const data: Interaction = await response.json();

	return data;
}

export async function endInteraction(interactionId: string) {
	const response = await fetch(`${PUBLIC_API_URL}/interaction/end`, {
		method: 'POST',
		headers: {
			'Content-Type': 'application/json',
			'x-api-key': PUBLIC_API_KEY
		},
		body: JSON.stringify({
			interactionId
		})
	});

	if (!response.ok) {
		throw new Error('Failed to end interaction');
	}

	const data: Interaction = await response.json();

	return data;
}

export async function logEvent(interactionId: string, name: string) {
	const response = await fetch(`${PUBLIC_API_URL}/interaction/event`, {
		method: 'POST',
		headers: {
			'Content-Type': 'application/json',
			'x-api-key': PUBLIC_API_KEY
		},
		body: JSON.stringify({
			interactionId,
			name
		})
	});

	if (!response.ok) {
		throw new Error('Failed to log event');
	}

	const data: Event = await response.json();

	return data;
}

export type VerifiedUser = {
	id: string;
	name: string;
	purpose: string;
	age: string;
	isDataCollected: boolean;
	rfid: string;
	quiz: string | null;
	isActive: boolean;
	createdAt: string;
	updatedAt: string;
};

export async function verifyUser(rfid: string) {
	const response = await fetch(`${PUBLIC_API_URL}/user/verify-user`, {
		method: 'POST',
		headers: {
			'Content-Type': 'application/json',
			'x-api-key': PUBLIC_API_KEY
		},
		body: JSON.stringify({ rfid })
	});

	if (!response.ok) {
		throw new Error('Failed to get verified user');
	}

	const data: VerifiedUser = await response.json();

	return data;
}

export async function submitScore(
	userId: string,
	score: number,
	result: { score: number; bonus: number }
) {
	const response = await fetch(`${PUBLIC_API_URL}/result`, {
		method: 'POST',
		headers: {
			'Content-Type': 'application/json',
			'x-api-key': PUBLIC_API_KEY
		},
		body: JSON.stringify({ userId, score, result: JSON.stringify(result) })
	});

	if (!response.ok) {
		throw new Error('Failed to submit score');
	}
}

export type Leaderboard = {
	id: string;
	userId: string;
	score: number;
	name: string;
	createdAt: string;
};

export async function getLeaderboard() {
	const response = await fetch(`${PUBLIC_API_URL}/result/leaderboard`, {
		method: 'GET',
		headers: {
			'Content-Type': 'application/json',
			'x-api-key': PUBLIC_API_KEY
		}
	});

	if (!response.ok) {
		throw new Error('Failed to get leaderboard');
	}

	const data: Leaderboard[] = await response.json();

	return data.slice(0, 9);
}
