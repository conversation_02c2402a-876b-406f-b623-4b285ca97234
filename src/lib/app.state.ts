import { writable } from 'svelte/store';

type AppStore = {
	language: 'en' | 'zh';
	interactionId: string;
	gameMode: 'solo' | 'multiplayer';
	gameStartBy: 'player1' | 'player2' | '';
	questionIndexs: number[];
};

type StyleType = 'A' | 'B';

export type Question = {
	question: string;
	answers: string[];
	answerStyleType: StyleType;
	correctAnswer: number;
	fact: string;
};

const answerStyleWrapperByType = (type: StyleType, content: string) => {
	switch (type) {
		case 'A':
			return `<span class="text-[30px] leading-[34px] tracking-[3.74px]">${content}</span>`;
		case 'B':
			return `<span class="text-[40px] leading-[34px] tracking-[3.74px]">${content}</span>`;
	}
};

export const QUESTIONS: Record<string, Question[]> = {
	en: [
		{
			question: 'Where are your <br/> kidneys located?',
			answers: ['Lower back', 'Below the <br/> intestines', 'In front of <br/> the stomach'],
			answerStyleType: 'A',
			correctAnswer: 0,
			fact: 'Kidneys are about the size of a fist and they filter about 180 liters of blood daily!!'
		},
		{
			question: 'What are the leading causes of <br/> kidney failure in Singapore?',
			answers: ['Arthritis', 'High cholesterol', 'Diabetes <br/> and hypertension'],
			answerStyleType: 'A',
			correctAnswer: 2,
			fact: 'Uncontrolled diabetes and hypertension can silently damage your kidneys if not managed properly.'
		},
		{
			question: 'What is the <br/> condition where the kidneys <br/> are not functioning properly?',
			answers: ['Kidney failure', 'Kidney stones', 'Kidney infection'],
			answerStyleType: 'A',
			correctAnswer: 0,
			fact: 'Chronic kidney disease often progresses silently and goes undetected until a much later stage.'
		},
		{
			question: 'What are the tubes <br/> that carry urine to the bladder?',
			answers: ['Pipes', 'Tubes', 'Ureters'],
			answerStyleType: 'B',
			correctAnswer: 2,
			fact: 'Each ureter is about as long as a ruler—around 25cm That’s how far pee travels from the kidney to the bladder!'
		},
		{
			question: 'How many nephrons does <br/> each kidney have?',
			answers: ['One million', 'One thousand', 'One hundred'],
			answerStyleType: 'B',
			correctAnswer: 0,
			fact: 'Each nephron is so tiny you need a microscope to see it, and damage to them is permanent.'
		},
		{
			question: 'How many stages of <br/> chronic kidney disease (CKD) are there?',
			answers: ['3 stages', '4 stages', '5 stages'],
			answerStyleType: 'B',
			correctAnswer: 2,
			fact: 'From stage 1, it is irreversible, but early detection and lifestyle changes can slow progression.'
		},
		{
			question: 'What does <span class="lowercase">e</span>GFR stands for?',
			answers: [
				'Estimated <br/> Gastrointestinal <br/> Function Rate',
				'Enhanced <br/> Glucose <br/> Filtration Rate',
				'Estimated <br/> Glomerular <br/> Filtration Rate'
			],
			answerStyleType: 'A',
			correctAnswer: 2,
			fact: 'Your eGFR is like a scorecard that shows how well your kidneys are cleaning your blood—even before you feel anything amiss.'
		},
		{
			question: 'Which of the following is not <br/> a kidney function?',
			answers: [
				'Regulate blood <br/> Pressure',
				'Filter blood & <br/> remove waste <br/> and toxins',
				'Digest food'
			],
			answerStyleType: 'A',
			correctAnswer: 2,
			fact: 'Kidneys are multitaskers! They filter blood, balance fluids, regulate blood pressure, produce red blood cells, and strengthen bones.'
		},
		{
			question: 'Which of the following is a <br/> healthy blood pressure reading?',
			answers: [
				'120/80<span class="font-phluff text-[20px]">mmHg</span>',
				'140/90<span class="font-phluff text-[20px]">mmHg</span>',
				'160/100<span class="font-phluff text-[20px]">mmHg</span>'
			],
			answerStyleType: 'B',
			correctAnswer: 0,
			fact: 'Your blood pressure fluctuates throughout the day due to factors like stress, food and activity.'
		},
		{
			question: 'What condition happens when <br/> there’s too much sugar in your blood?',
			answers: ['Hypertension', 'Diabetes', 'High cholesterol'],
			answerStyleType: 'A',
			correctAnswer: 1,
			fact: 'Pre-diabetes means your blood sugar is a bit high—but it can be reversed with healthy habits!'
		}
	].map((item) => ({
		...item,
		answerStyleType: item.answerStyleType as StyleType,
		answers: item.answers.map((answer) =>
			answerStyleWrapperByType(item.answerStyleType as StyleType, answer)
		)
	}))
};

export const appStore = writable<AppStore>({
	language: 'en',
	interactionId: '',
	gameMode: 'multiplayer',
	gameStartBy: '',
	questionIndexs: []
});

export const randomizeQuestions = (currentLanguage: string) => {
	const randomIndexs = QUESTIONS[currentLanguage].map((_, i) => i).sort(() => Math.random() - 0.5);

	appStore.update((store) => {
		store.questionIndexs = randomIndexs.slice(0, 5);
		return store;
	});
};

export const resetGame = () => {
	appStore.update((store) => {
		store.interactionId = '';
		store.gameMode = 'solo';
		store.gameStartBy = '';
		store.questionIndexs = [];
		return store;
	});
};
