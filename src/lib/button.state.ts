import { player1, player2 } from '$lib/player.state';
import { get } from 'svelte/store';

export function handleKeydown(event: KeyboardEvent) {
	if (['1', '2', '3', '4', '5', '6'].includes(event.key)) {
		if (+event.key <= 3) {
			if (get(player1).pressedKey !== -1) return;
			player1.update((state) => {
				state.pressedKey = +event.key - 1;
				return state;
			});
		} else {
			if (get(player2).pressedKey !== -1) return;
			player2.update((state) => {
				state.pressedKey = +event.key - 4;
				return state;
			});
		}
	}
}

export function handleKeyup(event: KeyboardEvent) {
	if (['1', '2', '3', '4', '5', '6'].includes(event.key)) {
		if (+event.key <= 3) {
			player1.update((state) => {
				state.pressedKey = -1;
				return state;
			});
		} else {
			player2.update((state) => {
				state.pressedKey = -1;
				return state;
			});
		}
	}
}
